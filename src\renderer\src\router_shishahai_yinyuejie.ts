import { createRouter, createWebHashHistory } from "vue-router";
import home from '@/views/shishahai_yinyuejie/home.vue'
import capture from '@/views/shishahai_yinyuejie/capture.vue'
import themeSelect from '@/views/shishahai_yinyuejie/themeSelect.vue'
import loading from '@/views/shishahai_yinyuejie/loading.vue'
import result from '@/views/shishahai_yinyuejie/result.vue'
import Login from "@/views/shishahai_yinyuejie/login.vue";
import { compile } from "vue";
import component from "element-plus/es/components/tree-select/src/tree-select-option.mjs";
import welcome from '@/views/shishahai_yinyuejie/welcome.vue'
import TakePhoto9_9 from "@/views/shishahai_yinyuejie/9_9Page/index.vue"
import FamilyHome from "@/views/shishahai_yinyuejie/family/index.vue"
import familyLoading from "@/views/shishahai_yinyuejie/family/generating.vue";
import familyResult from "@/views/shishahai_yinyuejie/family/result.vue";
import testImg from '@/views/shishahai_yinyuejie/result_makeImgtemp.vue';
import xingyun from '@/activityPage/xingyunshike.vue'
import path from "path";
const routes = [
  {
    path: '/xingyun',
    component: xingyun
  },
  {
    path: "/",
    redirect: "/login",
  },
  {
    path: "/welcome",
    name: "welcome",
    component: welcome,
  },
  {
   path: "/takePhoto9_9",
   name: "takePhoto9_9",
   component: TakePhoto9_9,
   meta:{
			title:"9.9活动",
			// 路由动画
			transition:'animate__fadeIn'
		}
  },
  {
    path: "/login",
    component: Login,
  },
  {
    path: "/home_capture",
    name: "home_capture",
    component: home,
  },
  {
    path: "/rebuild2-themeSelect",
    name: "rebuild2-themeSelect",
    component: themeSelect
  },
  // {
  //   path: "/rebuild2-capture",
  //   name: "rebuild2-capture",
  //   component: capture,
  // },
  {
    path: "/rebuild2-loading",
    name: "rebuild2-loading",
    component: loading,
  },
  {
    path: "/rebuild2-result",
    name: "rebuild2-result",
    component: result,
  },
  {
    path:'/family-home',
    name:'family-home',
    component:FamilyHome,
  },
  {
    path: '/family-result',
    name: 'family-result',
    component: familyResult,
  },
  {
    path: '/family-loading',
    name: 'family-loading',
    component: familyLoading,
  },
  {
    path: '/testImg',
    name: 'testImg',
    component: testImg,
  }
  
];
// console.log(VueRouter);
export default createRouter({
  // 4. Provide the history implementation to use. We are using the hash history for simplicity here.
  history: createWebHashHistory(),
  routes, // short for `routes: routes`
});
