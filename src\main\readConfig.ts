const fs = require('fs');
const path = require('path');
const { app } = require('electron');

const getConfigPath = () => {
    console.log('isPageaged', app.isPackaged)
  if (app.isPackaged) {
    // 打包后，config.json 文件在 exe 文件的同级目录下
    return path.join(path.dirname(app.getPath('exe')), 'config.json');
  } else {
    let _path = path.join(app.getAppPath(), './config.json');
    console.log(_path)
    // 开发环境下，config.json 文件在项目根目录下
    return _path
  }
};

const readConfig = () => {
  const configPath = getConfigPath();
  try {
    const configData = fs.readFileSync(configPath, 'utf-8');
    return JSON.parse(configData);
  } catch (error) {
    console.error('Error reading config file:', error);
    return {};
  }
};

const config = readConfig();
console.log(config);

export default config