import { ref } from 'vue'
import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import bus from '@/utils/bus'
// camera
// 初始化camera 获取视频流
// snap效果的数据流
const cameraSnapStream = ref(null)

// 普通效果的数据流
const cameraCommonStream = ref(null)

const timmer = ref(null);
const linkCount  = ref(0);

// 加载
const loadSnapStream = async () => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices()
    const snapCam = devices.find((d) => d.kind === 'videoinput' && d.label.includes('Snap'))
    console.log('访问到了么？', snapCam)
    if (snapCam) {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          deviceId: { exact: snapCam.deviceId },
          advanced: [
            { width: 3840, height: 2160 }, // 优先尝试4K
            { width: 1920, height: 1080 }, // 其次1080P
            { width: 1280, height: 720 } // 最后720P
          ]
        }
      })
      return stream
    }
  } catch (error) {
    console.error('摄像头访问失败:', error)
    ElMessage({
      message: '请检查摄像头权限和连接状态',
      type: 'error'
    })
  }
}
const loadCommonStream = async () => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices()
    const commonCam = devices.find((d) => d.kind === 'videoinput' && !d.label.includes('Snap'))
    if (commonCam) {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          deviceId: { exact: commonCam.deviceId },
          advanced: [
            { width: 3840, height: 2160 }, // 优先尝试4K
            { width: 1920, height: 1080 }, // 其次1080P
            { width: 1280, height: 720 } // 最后720P
          ]
        }
      })
      return stream
    }
  } catch (error) {
    console.error('摄像头访问失败:', error)
    ElMessage({
      message: '请检查摄像头权限和连接状态',
      type: 'error'
    })
  }
}

const setSnapStream = (data) => {
  cameraSnapStream.value = data
}
const setCommonStream = (data) => {
  cameraCommonStream.value = data
  cameraCommonStream.value.getTracks().forEach((track) => {
    console.log(track, 'TRACK状态')
    track.onended = () => {
      ElMessage({
        message: '摄像头已经断开,现在尝试重新连接',
        type: 'error'
      })
       bus.emit('reLinkError')
      // 每3秒连接一次,当重新连接上了,就关闭settimeout
       handleReLink();
    }
  })
}

const handleReLink = () => {
  // 每3秒连接一次,当重新连接上了,就关闭settimeout
  timmer.value = setTimeout(() => {
    loadCommonStream().then((res) => {
      if (res) {
        setCommonStream(res);
        ElMessage({
          message: '重新连接相机成功',
          type: 'success'
        })
        linkCount.value = 0;
        bus.emit('reLinkSuccess',res)
        clearTimeout(timmer.value)
        timmer.value = null
      } else {
        linkCount.value++
        ElMessage({
          message: `正在进行第${linkCount.value}次相机重连`,
          type: 'warning'
        })
        handleReLink()
      }
    })
  }, 3000)
}

// faceApi的实例化保存
export const useCameraStore = defineStore('cameraStore', () => {
  return {
    cameraSnapStream,
    cameraCommonStream,
    setSnapStream,
    setCommonStream,
    loadSnapStream,
    loadCommonStream
  }
})
